
import router from '@ohos.router';
import { Trip, TripType } from '../models/TripModel';
import { getTripById, updateTrip } from '../utils/TripUtils';

@Entry
@Component
struct EditTripPage {
  @State trip: Trip | null = null;
  @State startDate: Date = new Date();
  @State endDate: Date = new Date();

  tripTypes = [
    { type: TripType.LEISURE, name: '休闲旅游', icon: $r('app.media.icon_leisure') },
    { type: TripType.BUSINESS, name: '商务出行', icon: $r('app.media.icon_business') },
    { type: TripType.FAMILY, name: '家庭旅行', icon: $r('app.media.icon_family') },
    { type: TripType.ADVENTURE, name: '探险旅行', icon: $r('app.media.icon_adventure') },
    { type: TripType.CULTURAL, name: '文化之旅', icon: $r('app.media.icon_culture') },
    { type: TripType.ROMANTIC, name: '浪漫之旅', icon: $r('app.media.icon_romantic') }
  ];

  async aboutToAppear() {
    const params = router.getParams() as { tripId: string };
    if (params && params.tripId) {
      const tripId = parseInt(params.tripId, 10);
      this.trip = await getTripById(tripId);
      if (this.trip) {
        this.startDate = this.trip.startDate;
        this.endDate = this.trip.endDate;
      }
    }
  }

  async saveTrip() {
    if (this.trip) {
      this.trip.startDate = this.startDate;
      this.trip.endDate = this.endDate;
      await updateTrip(this.trip);
      router.back();
    }
  }

  build() {
    if (!this.trip) {
      Column() {
        Text('加载中...')
      }
    } else {
      Scroll() {
        Column() {
          Text('编辑行程')
            .fontSize(24)
            .fontWeight(FontWeight.Bold)
            .padding({ top: 20, bottom: 10 })

          TextInput({ text: this.trip.title, placeholder: '行程标题' })
            .onChange((value) => {
              if (this.trip) this.trip.title = value;
            })
            .margin({ bottom: 10 })

          TextInput({ text: this.trip.destination, placeholder: '目的地' })
            .onChange((value) => {
              if (this.trip) this.trip.destination = value;
            })
            .margin({ bottom: 10 })

          Text('行程类型').fontSize(18).fontWeight(FontWeight.Bold).margin({ bottom: 10 })
          Grid() {
            ForEach(this.tripTypes, (item) => {
              GridItem() {
                Column(
                  {
                    space: 8
                  }
                ) {
                  Image(item.icon).width(40).height(40)
                  Text(item.name).fontSize(12)
                }
                .padding(10)
                .backgroundColor(this.trip && this.trip.tripType === item.type ? '#E0F0E0' : '#F0F0F0')
                .cornerRadius(10)
                .onClick(() => {
                  if (this.trip) this.trip.tripType = item.type;
                })
              }
            })
          }
          .columnsTemplate('1fr 1fr 1fr')
          .columnsGap(10)
          .rowsGap(10)
          .padding({ bottom: 20 })


          DatePicker({
            start: new Date('2020-01-01'),
            end: new Date('2030-12-31'),
            selected: this.startDate
          })
            .onChange((date) => {
              this.startDate.setFullYear(date.year, date.month, date.day);
            })

          DatePicker({
            start: new Date('2020-01-01'),
            end: new Date('2030-12-31'),
            selected: this.endDate
          })
            .onChange((date) => {
              this.endDate.setFullYear(date.year, date.month, date.day);
            })

          TextArea({ text: this.trip.description, placeholder: '行程描述' })
            .onChange((value) => {
              if (this.trip) this.trip.description = value;
            })
            .height(100)
            .margin({ top: 20 })

          Toggle({ type: ToggleType.Switch, isOn: this.trip.isPublic })
            .onChange((isOn) => {
              if (this.trip) this.trip.isPublic = isOn;
            })
          Text('公开行程')

          Row() {
            Button('取消')
              .onClick(() => {
                router.back();
              })
              .layoutWeight(1)
              .margin({ right: 10 })
              .backgroundColor(Color.Gray)

            Button('保存更改')
              .onClick(() => {
                this.saveTrip();
              })
              .layoutWeight(1)
              .backgroundColor(Color.Green)
          }
          .margin({ top: 20 })
        }
        .padding({ left: 20, right: 20 })
      }
    }
  }
}
