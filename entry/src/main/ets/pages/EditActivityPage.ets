/**
 * 编辑活动页面
 * 用于编辑现有活动的信息
 */

import { Activity, ActivityType, TripDataManager } from '../models/TripModel';
import { ActivityForm, ActivityFormData } from '../components/ActivityForm';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
  activityId: number;
  date?: string;
}

@Entry
@Component
struct EditActivityPage {
  @State tripId: number = 0;
  @State activityId: number = 0;
  @State activity: Activity | null = null;
  @State formData: ActivityFormData = {
    title: '',
    description: '',
    location: '',
    type: ActivityType.SIGHTSEEING,
    startTime: '',
    endTime: ''
  };
  @State isSubmitting: boolean = false;
  @State showDeleteConfirm: boolean = false;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('EditActivityPage: aboutToAppear 被调用');

    // 获取路由参数
    const rawParams: unknown = router.getParams();
    if (rawParams && typeof rawParams === 'object') {
      const routeParams: RouteParams = rawParams as RouteParams;
      if (routeParams.tripId !== undefined && routeParams.activityId !== undefined) {
        this.tripId = routeParams.tripId;
        this.activityId = routeParams.activityId;
        
        console.log(`EditActivityPage: 行程ID: ${this.tripId}, 活动ID: ${this.activityId}`);
        
        // 加载活动数据
        this.loadActivityData();
      }
    } else {
      console.error('EditActivityPage: 未获取到有效的路由参数');
    }
  }

  // 加载活动数据
  loadActivityData() {
    const result = this.tripManager.getActivityById(this.tripId, this.activityId);
    if (result.activity) {
      this.activity = result.activity;
      
      // 填充表单数据
      this.formData = {
        title: result.activity.title,
        description: result.activity.description || '',
        location: result.activity.location || '',
        type: result.activity.type,
        startTime: result.activity.startTime,
        endTime: result.activity.endTime
      };
      
      console.log(`EditActivityPage: 成功加载活动: ${result.activity.title}`);
    } else {
      console.error(`EditActivityPage: 未找到ID为 ${this.activityId} 的活动`);
    }
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理保存活动
  handleSaveActivity = () => {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // 更新活动数据
      const updatedData: Partial<ActivityInput> = {
        title: this.formData.title,
        description: this.formData.description,
        startTime: this.formData.startTime,
        endTime: this.formData.endTime,
        location: this.formData.location,
        type: this.formData.type
      };

      // 更新活动
      const success = this.tripManager.updateActivity(this.tripId, this.activityId, updatedData);
      
      if (success) {
        console.log(`EditActivityPage: 成功更新活动`);
        
        // 显示成功提示
        // TODO: 添加Toast提示
        
        // 返回上一页
        router.back();
      } else {
        console.error('EditActivityPage: 更新活动失败');
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('EditActivityPage: 更新活动时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.isSubmitting = false;
    }
  }

  // 处理删除活动
  handleDeleteActivity = () => {
    this.showDeleteConfirm = true;
  }

  // 确认删除活动
  confirmDeleteActivity = () => {
    try {
      const success = this.tripManager.deleteActivity(this.tripId, this.activityId);
      
      if (success) {
        console.log(`EditActivityPage: 成功删除活动`);
        
        // 显示成功提示
        // TODO: 添加Toast提示
        
        // 返回上一页
        router.back();
      } else {
        console.error('EditActivityPage: 删除活动失败');
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('EditActivityPage: 删除活动时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.showDeleteConfirm = false;
    }
  }

  // 验证表单
  validateForm(): boolean {
    if (!this.formData.title.trim()) {
      console.error('活动标题不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.startTime) {
      console.error('开始时间不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.endTime) {
      console.error('结束时间不能为空');
      // TODO: 显示错误提示
      return false;
    }

    return true;
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .onClick(this.handleBack)

        Blank()

        Text('编辑活动')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)

        Blank()

        // 删除按钮
        Button() {
          IconText({
            iconText: '🗑️',
            fontSize: 16,
            fontColor: '#FF4444',
            iconWidth: 16,
            iconHeight: 16
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor('#FFE6E6')
        .onClick(this.handleDeleteActivity)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(THEME_COLORS.cardBackground)

      if (this.activity) {
        Scroll() {
          Column() {
            // 活动信息卡片
            Column() {
              Row() {
                Text('✏️')
                  .fontSize(24)
                  .margin({ right: 12 })

                Column() {
                  Text(this.activity.title)
                    .fontSize(16)
                    .fontWeight(600)
                    .fontColor(THEME_COLORS.textPrimary)
                    .alignSelf(ItemAlign.Start)
                    .maxLines(1)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })

                  Text('正在编辑活动信息')
                    .fontSize(14)
                    .fontColor(THEME_COLORS.textSecondary)
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 2 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)

                Text('编辑中')
                  .fontSize(12)
                  .fontColor('#FF8C00')
                  .backgroundColor('#FFF3E0')
                  .borderRadius(12)
                  .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              }
              .width('100%')
              .alignItems(VerticalAlign.Center)
            }
            .width('100%')
            .backgroundColor(THEME_COLORS.cardBackground)
            .borderRadius(12)
            .padding(16)
            .margin({ top: 16, bottom: 16 })

            // 活动表单
            ActivityForm({ formData: $formData })
          }
          .width('100%')
        }
        .layoutWeight(1)

        // 底部操作按钮
        Row() {
          Button('取消')
            .type(ButtonType.Normal)
            .backgroundColor('#F5F5F5')
            .fontColor(THEME_COLORS.textSecondary)
            .borderRadius(12)
            .layoutWeight(1)
            .height(48)
            .onClick(this.handleBack)

          Button(this.isSubmitting ? '保存中...' : '保存修改')
            .type(ButtonType.Normal)
            .backgroundColor(THEME_COLORS.primary)
            .fontColor(Color.White)
            .borderRadius(12)
            .layoutWeight(1)
            .height(48)
            .margin({ left: 12 })
            .enabled(!this.isSubmitting)
            .onClick(this.handleSaveActivity)
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 16 })
        .backgroundColor(THEME_COLORS.cardBackground)
      }

      // 删除确认对话框
      if (this.showDeleteConfirm) {
        Stack() {
          // 背景遮罩
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('rgba(0, 0, 0, 0.5)')
            .onClick(() => {
              this.showDeleteConfirm = false;
            })

          // 确认对话框
          Column() {
            Text('确认删除')
              .fontSize(18)
              .fontWeight(600)
              .fontColor(THEME_COLORS.textPrimary)
              .margin({ bottom: 12 })

            Text('确定要删除这个活动吗？删除后无法恢复。')
              .fontSize(14)
              .fontColor(THEME_COLORS.textSecondary)
              .textAlign(TextAlign.Center)
              .margin({ bottom: 24 })

            Row() {
              Button('取消')
                .type(ButtonType.Normal)
                .backgroundColor('#F5F5F5')
                .fontColor(THEME_COLORS.textSecondary)
                .borderRadius(8)
                .layoutWeight(1)
                .height(40)
                .onClick(() => {
                  this.showDeleteConfirm = false;
                })

              Button('删除')
                .type(ButtonType.Normal)
                .backgroundColor('#FF4444')
                .fontColor(Color.White)
                .borderRadius(8)
                .layoutWeight(1)
                .height(40)
                .margin({ left: 12 })
                .onClick(this.confirmDeleteActivity)
            }
            .width('100%')
          }
          .width('80%')
          .backgroundColor(Color.White)
          .borderRadius(12)
          .padding(24)
        }
        .width('100%')
        .height('100%')
        .position({ x: 0, y: 0 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }
}
