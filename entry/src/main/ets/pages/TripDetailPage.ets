/**
 * 行程详情页面
 * 显示行程概览，包括顶部卡片、快速操作和行程安排
 */

import { Trip, DailyItinerary, QuickAction, Activity, QuickActionType } from '../models/TripModel';
import { QuickActionGrid } from '../components/QuickActionGrid';
import { ItineraryList } from '../components/ItineraryList';
import { getTripById, getStatusLabel, getStatusColor, getTripTypeLabel, formatDateRange, THEME_COLORS, calculateDays } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: string;
}

@Entry
@Component
struct TripDetailPage {
  @State trip: Trip | null = null;
  @State itineraries: DailyItinerary[] = [];
  @State quickActions: QuickAction[] = [];

  async aboutToAppear() {
    console.log('TripDetailPage: aboutToAppear 被调用');
    const params = router.getParams() as RouteParams;
    console.log('TripDetailPage: 路由参数:', JSON.stringify(params));

    if (params && params.tripId) {
      const tripId = parseInt(params.tripId, 10);
      console.log(`TripDetailPage: 获取行程ID: ${tripId}`);
      const foundTrip = await getTripById(tripId);
      this.trip = foundTrip || null;

      if (this.trip) {
        console.log(`TripDetailPage: 找到行程: ${this.trip.title}`);
        this.itineraries = this.groupActivitiesByDay(this.trip.activities);
        this.quickActions = this.getStaticQuickActions();
        console.log(`TripDetailPage: 加载了 ${this.itineraries.length} 天行程`);
        console.log(`TripDetailPage: 加载了 ${this.quickActions.length} 个快速操作`);
      } else {
        console.error(`TripDetailPage: 未找到ID为 ${tripId} 的行程`);
      }
    } else {
      console.error('TripDetailPage: 未获取到有效的路由参数');
    }
  }

  groupActivitiesByDay(activities: Activity[]): DailyItinerary[] {
    if (!activities) return [];
    const grouped = new Map<string, Activity[]>();
    activities.forEach(activity => {
      const dateStr = activity.date.toISOString().split('T')[0];
      if (!grouped.has(dateStr)) {
        grouped.set(dateStr, []);
      }
      grouped.get(dateStr)?.push(activity);
    });

    const result: DailyItinerary[] = [];
    let dayNumber = 1;
    grouped.forEach((activities, date) => {
      result.push({
        dayNumber: dayNumber++,
        date: new Date(date),
        title: `第 ${dayNumber - 1} 天`,
        activities: activities
      });
    });
    return result;
  }

  getStaticQuickActions(): QuickAction[] {
    const actions: QuickAction[] = [
      { id: 1, title: '每日行程', icon: 'icon_daily_itinerary', type: QuickActionType.DAILY_ITINERARY },
      { id: 2, title: '添加活动', icon: 'icon_add_activity', type: QuickActionType.ADD_ACTIVITY },
    ];
    return actions;
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理快速操作点击
  handleQuickActionClick = (action: QuickAction) => {
    console.log(`点击了快速操作: ${action.title}`);

    // 根据不同的操作类型进行相应的处理
    switch (action.type) {
      case QuickActionType.DAILY_ITINERARY:
        // 跳转到每日行程页面
        this.navigateToDailyItinerary();
        break;
      case QuickActionType.ADD_ACTIVITY:
        console.log('跳转到添加活动页面');
        this.handleAddActivity();
        break;
      default:
        console.log(`未处理的快速操作类型: ${action.type}`);
    }
  }

  // 跳转到每日行程页面
  navigateToDailyItinerary = () => {
    if (!this.trip) {
      console.error('TripDetailPage: 无法跳转到每日行程页面，行程信息为空');
      return;
    }

    console.log(`TripDetailPage: 跳转到每日行程页面，行程ID: ${this.trip.id}`);

    router.pushUrl({
      url: 'pages/DailyItineraryPage',
      params: {
        tripId: this.trip.id.toString(),
        selectedDate: this.itineraries.length > 0 ? this.itineraries[0].date.toISOString() : undefined
      }
    }).then(() => {
      console.log('TripDetailPage: 成功跳转到每日行程页面');
    }).catch((error: Error) => {
      console.error('TripDetailPage: 跳转到每日行程页面失败:', error);
      console.error('TripDetailPage: 错误详情:', JSON.stringify(error));
    });
  }

  // 处理活动点击
  handleActivityClick = (activity: Activity) => {
    console.log(`点击了活动: ${activity.title}`);

    if (!this.trip) {
      console.error('TripDetailPage: 无法跳转到活动详情页面，行程信息为空');
      return;
    }

    console.log(`TripDetailPage: 跳转到活动详情页面，活动ID: ${activity.id}`);

    router.pushUrl({
      url: 'pages/ActivityDetailPage',
      params: {
        tripId: this.trip.id.toString(),
        activityId: activity.id.toString()
      }
    }).then(() => {
      console.log('TripDetailPage: 成功跳转到活动详情页面');
    }).catch((error: Error) => {
      console.error('TripDetailPage: 跳转到活动详情页面失败:', error);
      console.error('TripDetailPage: 错误详情:', JSON.stringify(error));
    });
  }

  // 处理日期点击
  handleDayClick = (day: DailyItinerary) => {
    console.log(`点击了第${day.dayNumber}天: ${day.title}`);

    if (!this.trip) {
      console.error('TripDetailPage: 无法跳转到每日行程页面，行程信息为空');
      return;
    }

    console.log(`TripDetailPage: 跳转到每日行程页面，日期: ${day.date}`);

    router.pushUrl({
      url: 'pages/DailyItineraryPage',
      params: {
        tripId: this.trip.id.toString(),
        selectedDate: day.date.toISOString()
      }
    }).then(() => {
      console.log('TripDetailPage: 成功跳转到每日行程页面');
    }).catch((error: Error) => {
      console.error('TripDetailPage: 跳转到每日行程页面失败:', error);
      console.error('TripDetailPage: 错误详情:', JSON.stringify(error));
    });
  }

  // 处理添加新的一天
  handleAddNewDay = () => {
    console.log('点击添加新的一天');
    // 这里可以添加新的一天的逻辑
  }

  // 处理添加活动
  handleAddActivity = () => {
    console.log('TripDetailPage: 点击添加活动');

    if (!this.trip) {
      console.error('TripDetailPage: 无法添加活动，行程信息为空');
      return;
    }

    // 获取第一天的日期作为默认日期
    if (this.itineraries.length > 0) {
      const firstDay = this.itineraries[0];

      console.log(`TripDetailPage: 跳转到添加活动页面，日期: ${firstDay.date}`);

      router.pushUrl({
        url: 'pages/AddActivityPage',
        params: {
          tripId: this.trip.id.toString(),
          date: firstDay.date.toISOString(),
          dayTitle: firstDay.title
        }
      }).then(() => {
        console.log('TripDetailPage: 成功跳转到添加活动页面');
      }).catch((error: Error) => {
        console.error('TripDetailPage: 跳转到添加活动页面失败:', error);
        console.error('TripDetailPage: 错误详情:', JSON.stringify(error));
      });
    } else {
      console.error('TripDetailPage: 没有可用的行程日期');
    }
  }

  // 获取完成的活动数量
  getCompletedActivitiesCount(): number {
    if (!this.trip || !this.trip.activities) {
      return 0;
    }
    return this.trip.activities.filter(a => a.completed).length;
  }

  // 获取完成百分比
  getCompletionPercentage(): number {
    if (!this.trip || !this.trip.activities || this.trip.activities.length === 0) {
      return 0;
    }
    const completed = this.trip.activities.filter(a => a.completed).length;
    return Math.round((completed / this.trip.activities.length) * 100);
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .onClick(this.handleBack)

        Blank()

        Text(this.trip?.title || '巴黎浪漫之旅')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)

        Blank()

        // 占位，保持标题居中
        Button()
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .visibility(Visibility.Hidden)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(THEME_COLORS.cardBackground)

      if (this.trip) {
        Scroll() {
          Column() {
            // 行程卡片
            Column() {
              // 卡片头部
              Row() {
                Column() {
                  Text(this.trip.title)
                    .fontSize(20)
                    .fontWeight(600)
                    .fontColor(Color.White)
                    .alignSelf(ItemAlign.Start)
                    .maxLines(1)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })

                  Text(this.trip.destination)
                    .fontSize(14)
                    .fontColor('rgba(255, 255, 255, 0.8)')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)

                // 编辑按钮
                Button() {
                  IconText({
                    iconText: '✏️',
                    fontSize: 16,
                    fontColor: '#FFFFFF',
                    iconWidth: 16,
                    iconHeight: 16
                  })
                }
                .type(ButtonType.Circle)
                .backgroundColor('rgba(255, 255, 255, 0.2)')
                .width(32)
                .height(32)
                .onClick(() => {
                  if(this.trip) {
                    router.pushUrl({ url: 'pages/EditTripPage', params: { tripId: this.trip.id.toString() } });
                  }
                })
              }
              .width('100%')
              .alignItems(VerticalAlign.Top)
              .margin({ bottom: 16 })

              // 日期和进度信息
              Row() {
                Column() {
                  Text('出发日期')
                    .fontSize(12)
                    .fontColor('rgba(255, 255, 255, 0.7)')

                  Text(this.trip.startDate.toLocaleDateString())
                    .fontSize(14)
                    .fontWeight(500)
                    .fontColor(Color.White)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.Start)

                Blank()

                Column() {
                  Text('天数')
                    .fontSize(12)
                    .fontColor('rgba(255, 255, 255, 0.7)')

                  Text(`${calculateDays(this.trip.startDate, this.trip.endDate)}天`)
                    .fontSize(14)
                    .fontWeight(500)
                    .fontColor(Color.White)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.Center)

                Blank()

                Column() {
                  Text('进度')
                    .fontSize(12)
                    .fontColor('rgba(255, 255, 255, 0.7)')

                  Text(`${this.getCompletionPercentage()}%`)
                    .fontSize(14)
                    .fontWeight(500)
                    .fontColor(Color.White)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.End)
              }
              .width('100%')
              .margin({ bottom: 12 })

              // 进度条
              Progress({ value: this.getCompletedActivitiesCount(), total: this.trip.activities.length, type: ProgressType.Linear })
                .width('100%')
                .height(4)
                .color(Color.White)
                .backgroundColor('rgba(255, 255, 255, 0.3)')
            }
            .width('100%')
            .padding(20)
            .backgroundColor(THEME_COLORS.primary)
            .borderRadius(16)
            .margin({ left: 16, right: 16, top: 16, bottom: 16 })

            // 快速操作
            QuickActionGrid({
              quickActions: this.quickActions,
              onActionClick: this.handleQuickActionClick
            })

            // 行程安排
            ItineraryList({
              itineraries: this.itineraries,
              onActivityClick: this.handleActivityClick,
              onDayClick: this.handleDayClick
            })

            // 添加新的一天按钮
            Button() {
              Row() {
                Text('➕')
                  .fontSize(16)
                  .fontColor(THEME_COLORS.primary)
                  .margin({ right: 8 })

                Text('添加新的一天')
                  .fontSize(14)
                  .fontColor(THEME_COLORS.primary)
              }
              .alignItems(VerticalAlign.Center)
            }
            .type(ButtonType.Normal)
            .backgroundColor('#E8F5E8')
            .borderRadius(8)
            .width('calc(100% - 32vp)')
            .height(48)
            .margin({ left: 16, right: 16, top: 16, bottom: 16 })
            .onClick(this.handleAddNewDay)
          }
        }
        .layoutWeight(1)
        .scrollBar(BarState.Off)
      } else {
        // 错误状态
        Column() {
          Text('行程信息加载失败')
            .fontSize(16)
            .fontColor(THEME_COLORS.textSecondary)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)
  }
}
