/**
 * 行程相关的工具函数
 * 包含状态标签、颜色、日期格式化等实用函数
 */

import { Trip, TripStatus, TripType, Activity, ActivityType } from '../models/TripModel';

// Mock Database for trips
let trips: Trip[] = [
  {
    id: 1,
    title: '巴黎浪漫之旅',
    destination: '法国巴黎',
    startDate: new Date('2024-07-15'),
    endDate: new Date('2024-07-22'),
    description: '探索浪漫之都巴黎的经典景点和文化魅力。',
    tripType: TripType.LEISURE,
    isPublic: true,
    defaultActivityDuration: 60,
    status: TripStatus.UPCOMING,
    daysCount: 8,
    progress: 25,
    activities: [
      {
        id: 1,
        tripId: 1,
        title: '参观埃菲尔铁塔',
        date: new Date('2024-07-16'),
        type: ActivityType.SIGHTSEEING,
        startTime: '09:00',
        endTime: '11:00',
        completed: true
      },
      {
        id: 2,
        tripId: 1,
        title: '卢浮宫艺术鉴赏',
        date: new Date('2024-07-17'),
        type: ActivityType.SIGHTSEEING,
        startTime: '14:00',
        endTime: '16:00',
        completed: false
      }
    ]
  },
  {
    id: 2,
    title: '东京科技探索',
    destination: '日本东京',
    startDate: new Date('2024-08-01'),
    endDate: new Date('2024-08-08'),
    description: '体验东京的未来科技和传统文化。',
    tripType: TripType.LEISURE,
    isPublic: false,
    defaultActivityDuration: 90,
    status: TripStatus.UPCOMING,
    daysCount: 8,
    progress: 0,
    activities: []
  }
];

let nextTripId = 3;

/**
 * 获取所有行程
 * @returns 所有行程的列表
 */
export async function getAllTrips(): Promise<Trip[]> {
  return Promise.resolve(deepClone(trips));
}

/**
 * 按ID获取行程
 * @param id 行程ID
 * @returns 行程对象或null
 */
export async function getTripById(id: number): Promise<Trip | null> {
  const trip = trips.find(t => t.id === id);
  return Promise.resolve(trip ? deepClone(trip) : null);
}

/**
 * 添加新行程
 * @param trip 新行程的数据
 * @returns 创建的行程对象
 */
export async function addTrip(trip: Trip): Promise<Trip> {
  const newTrip: Trip = {
    id: nextTripId++,
    title: trip.title,
    destination: trip.destination,
    startDate: trip.startDate,
    endDate: trip.endDate,
    description: trip.description,
    tripType: trip.tripType,
    isPublic: trip.isPublic,
    defaultActivityDuration: trip.defaultActivityDuration,
    status: TripStatus.UPCOMING,
    daysCount: Math.ceil((trip.endDate.getTime() - trip.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1,
    progress: 0,
    activities: trip.activities || []
  };
  trips.push(newTrip);
  return Promise.resolve(deepClone(newTrip));
}


/**
 * 更新行程
 * @param updatedTrip 更新后的行程对象
 * @returns 更新后的行程对象或null
 */
export async function updateTrip(updatedTrip: Trip): Promise<Trip | null> {
  const index = trips.findIndex(t => t.id === updatedTrip.id);
  if (index !== -1) {
    trips[index] = deepClone(updatedTrip);
    return Promise.resolve(deepClone(updatedTrip));
  }
  return Promise.resolve(null);
}

// 状态标签映射
const STATUS_LABELS: Record<string, string> = {
  'UPCOMING': '即将开始',
  'IN_PROGRESS': '进行中',
  'COMPLETED': '已完成'
};

// 状态颜色映射
const STATUS_COLORS: Record<string, string> = {
  'UPCOMING': '#007AFF',
  'IN_PROGRESS': '#FF9500',
  'COMPLETED': '#34C759'
};

// 行程类型标签映射
const TRIP_TYPE_LABELS: Record<string, string> = {
  'BUSINESS': '商务',
  'LEISURE': '休闲',
  'FAMILY': '家庭'
};

// 活动类型标签映射
const ACTIVITY_TYPE_LABELS: Record<string, string> = {
  'SIGHTSEEING': '观光',
  'DINING': '用餐',
  'SHOPPING': '购物',
  'TRANSPORTATION': '交通',
  'ACCOMMODATION': '住宿',
  'ENTERTAINMENT': '娱乐',
  'OTHER': '其他'
};

// 活动状态标签映射
const ACTIVITY_STATUS_LABELS: Record<string, string> = {
  'completed': '已完成',
  'pending': '即将开始',
  'in-progress': '进行中',
  'cancelled': '已取消'
};

// 主题颜色接口
export interface ThemeColors {
  primary: string;
  primaryLight: string;
  secondary: string;
  background: string;
  cardBackground: string;
  textPrimary: string;
  textSecondary: string;
  border: string;
  success: string;
}

// 主题颜色
export const THEME_COLORS: ThemeColors = {
  primary: '#14b8a6',
  primaryLight: '#14b8a615',
  secondary: '#8e8e93',
  background: '#f8f9fa',
  cardBackground: '#ffffff',
  textPrimary: '#000000',
  textSecondary: '#8e8e93',
  border: 'rgba(60, 60, 67, 0.12)',
  success: '#10b981'
};

/**
 * 获取状态标签
 * @param status 状态值
 * @returns 状态标签
 */
export function getStatusLabel(status: string): string {
  return STATUS_LABELS[status] || '未知';
}

/**
 * 获取状态颜色
 * @param status 状态值
 * @returns 状态颜色
 */
export function getStatusColor(status: string): string {
  return STATUS_COLORS[status] || '#8E8E93';
}

/**
 * 获取行程类型标签
 * @param type 行程类型
 * @returns 类型标签
 */
export function getTripTypeLabel(type: TripType): string {
  return TRIP_TYPE_LABELS[type.toString()] || '其他';
}

/**
 * 获取活动类型标签
 * @param type 活动类型
 * @returns 类型标签
 */
export function getActivityTypeLabel(type: ActivityType): string {
  return ACTIVITY_TYPE_LABELS[type.toString()] || '其他';
}

/**
 * 获取活动状态标签
 * @param completed 是否完成
 * @returns 状态标签
 */
export function getActivityStatusLabel(completed: boolean): string {
  return completed ? '已完成' : '即将开始';
}

/**
 * 获取活动类型图标
 * @param type 活动类型
 * @returns 图标字符串
 */
export function getActivityTypeIcon(type: ActivityType): string {
  switch (type) {
    case ActivityType.SIGHTSEEING:
      return '🏛️';
    case ActivityType.DINING:
      return '🍽️';
    case ActivityType.SHOPPING:
      return '🛍️';
    case ActivityType.TRANSPORTATION:
      return '✈️';
    case ActivityType.ACCOMMODATION:
      return '🏨';
    case ActivityType.ENTERTAINMENT:
      return '🎭';
    default:
      return '📍';
  }
}

/**
 * 格式化日期范围
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 格式化的日期范围字符串
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
  const startMonth = startDate.getMonth() + 1;
  const startDay = startDate.getDate();
  const endMonth = endDate.getMonth() + 1;
  const endDay = endDate.getDate();

  if (startMonth === endMonth) {
    return `${startMonth}月${startDay}-${endDay}日`;
  } else {
    return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
  }
}

/**
 * 格式化单个日期
 * @param date 日期
 * @returns 格式化的日期
 */
export function formatDate(date: Date): string {
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
}

/**
 * 计算两个日期之间的天数
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数
 */
export function calculateDays(startDate: Date, endDate: Date): number {
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
}

/**
 * 获取进度条颜色
 * @param progress 进度值
 * @param status 状态
 * @returns 进度条颜色
 */
export function getProgressColor(progress: number, status: string): string {
  if (progress === 100) {
    return STATUS_COLORS[TripStatus.COMPLETED];
  }
  return getStatusColor(status);
}

/**
 * 验证日期格式
 * @param date 日期
 * @returns 是否为有效日期
 */
export function isValidDate(date: Date): boolean {
  return !isNaN(date.getTime());
}

/**
 * 获取相对时间描述
 * @param date 日期
 * @returns 相对时间描述
 */
export function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return `${Math.abs(diffDays)}天前`;
  } else if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '明天';
  } else if (diffDays <= 7) {
    return `${diffDays}天后`;
  } else if (diffDays <= 30) {
    const weeks = Math.ceil(diffDays / 7);
    return `${weeks}周后`;
  } else {
    const months = Math.ceil(diffDays / 30);
    return `${months}个月后`;
  }
}

/**
 * 生成唯一ID
 * @returns 唯一ID
 */
export function generateId(): number {
  return Date.now() + Math.floor(Math.random() * 1000);
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj), (key: string, value: string | number | boolean | object | null): string | number | boolean | object | null | Date => {
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(value)) {
      return new Date(value);
    }
    return value;
  }) as T;
}
