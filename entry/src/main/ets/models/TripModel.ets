/**
 * 行程数据模型
 * 包含行程相关的类型定义和数据管理
 */

// 行程状态枚举
export enum TripStatus {
  UPCOMING = 'upcoming',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed'
}

// 行程类型枚举
export enum TripType {
  LEISURE,
  BUSINESS,
  FAMILY,
  ADVENTURE,
  CULTURAL,
  ROMANTIC
}

// 活动类型枚举
export enum ActivityType {
  SIGHTSEEING = 'sightseeing',
  DINING = 'dining',
  SHOPPING = 'shopping',
  TRANSPORTATION = 'transportation',
  ACCOMMODATION = 'accommodation',
  ENTERTAINMENT = 'entertainment',
  OTHER = 'other'
}

// 活动项目接口
export interface Activity {
  id: number;
  tripId: number;
  title: string;
  description?: string;
  location?: string;
  date: Date;
  type: ActivityType;
  startTime: string;
  endTime: string;
  completed: boolean;
}

// 行程接口定义
export interface Trip {
  id: number;
  title: string;
  destination: string;
  startDate: Date;
  endDate: Date;
  description: string;
  tripType: TripType;
  isPublic: boolean;
  defaultActivityDuration: number;
  activities: Activity[];
  status: TripStatus;
  daysCount: number;
  progress: number;
}

// 每日行程接口
export interface DailyItinerary {
  date: Date;
  dayNumber: number;
  title: string;
  activities: Activity[];
}

// 活动输入接口 (用于创建或更新活动)
export interface ActivityInput {
  title: string;
  description?: string;
  location?: string;
  type: ActivityType;
  startTime: string;
  endTime: string;
  completed: boolean;
}

// 活动查询结果接口
export interface ActivityQueryResult {
  trip: Trip | null;
  activity: Activity | null;
}

// 活动更新数据接口
export interface ActivityUpdateData {
  title?: string;
  description?: string;
  location?: string;
  type?: ActivityType;
  startTime?: string;
  endTime?: string;
  completed?: boolean;
}

// 行程数据管理器 (模拟数据库操作)
export class TripDataManager {
  private trips: Trip[] = [
    {
      id: 1,
      title: '巴黎浪漫之旅',
      destination: '法国巴黎',
      startDate: new Date('2024-07-15'),
      endDate: new Date('2024-07-22'),
      description: '探索浪漫之都巴黎的经典景点和文化魅力。',
      tripType: TripType.ROMANTIC,
      isPublic: true,
      defaultActivityDuration: 60,
      activities: [
        { id: 1, tripId: 1, title: '参观埃菲尔铁塔', date: new Date('2024-07-16'), type: ActivityType.SIGHTSEEING, startTime: '09:00', endTime: '11:00', completed: true },
        { id: 2, tripId: 1, title: '卢浮宫艺术鉴赏', date: new Date('2024-07-17'), type: ActivityType.SIGHTSEEING, startTime: '14:00', endTime: '17:00', completed: false },
      ],
      status: TripStatus.UPCOMING,
      daysCount: 8,
      progress: 25
    },
    {
      id: 2,
      title: '东京科技探索',
      destination: '日本东京',
      startDate: new Date('2024-08-01'),
      endDate: new Date('2024-08-08'),
      description: '体验东京的未来科技和传统文化。',
      tripType: TripType.ADVENTURE,
      isPublic: false,
      defaultActivityDuration: 90,
      activities: [],
      status: TripStatus.UPCOMING,
      daysCount: 8,
      progress: 0
    }
  ];

  private nextTripId: number = 3;
  private nextActivityId: number = 100;

  constructor() {
    // 初始化时计算所有行程的 daysCount 和 progress
    this.trips.forEach(trip => {
      trip.daysCount = this.calculateDays(trip.startDate, trip.endDate);
      trip.progress = this.calculateTripProgress(trip);
    });
  }

  // 获取所有行程
  getAllTrips(): Trip[] {
    return this.deepClone(this.trips);
  }

  // 按ID获取行程
  getTripById(id: number): Trip | null {
    const trip = this.trips.find(t => t.id === id);
    return trip ? this.deepClone(trip) : null;
  }

  // 添加新行程
  addTrip(trip: Trip): Trip {
    const newTrip: Trip = {
      id: this.nextTripId++,
      title: trip.title,
      destination: trip.destination,
      startDate: trip.startDate,
      endDate: trip.endDate,
      description: trip.description || '',
      tripType: trip.tripType,
      isPublic: trip.isPublic || false,
      defaultActivityDuration: trip.defaultActivityDuration || 60,
      activities: trip.activities || [],
      status: TripStatus.UPCOMING, // 新增行程默认为即将开始
      daysCount: this.calculateDays(trip.startDate, trip.endDate),
      progress: 0 // 新增行程进度为0
    };
    this.trips.push(newTrip);
    return this.deepClone(newTrip);
  }

  // 更新行程
  updateTrip(updatedTrip: Trip): Trip | null {
    const index = this.trips.findIndex(t => t.id === updatedTrip.id);
    if (index !== -1) {
      // 重新计算 daysCount 和 progress
      updatedTrip.daysCount = this.calculateDays(updatedTrip.startDate, updatedTrip.endDate);
      updatedTrip.progress = this.calculateTripProgress(updatedTrip);
      this.trips[index] = this.deepClone(updatedTrip);
      return this.deepClone(updatedTrip);
    }
    return null;
  }

  // 删除行程
  deleteTrip(id: number): boolean {
    const initialLength = this.trips.length;
    this.trips = this.trips.filter(t => t.id !== id);
    return this.trips.length < initialLength;
  }

  // 获取行程的每日行程详情
  getTripDetails(tripId: number): DailyItinerary[] {
    const trip = this.getTripById(tripId);
    if (!trip) {
      return [];
    }

    const dailyItineraries: DailyItinerary[] = [];
    const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数

    for (let d = new Date(trip.startDate); d <= trip.endDate; d.setDate(d.getDate() + 1)) {
      const currentDate = new Date(d); // 创建新的Date对象以避免引用问题
      const dayActivities = trip.activities.filter(activity =>
        activity.date.toDateString() === currentDate.toDateString()
      ).sort((a, b) => a.startTime.localeCompare(b.startTime)); // 按时间排序

      dailyItineraries.push({
        date: currentDate,
        dayNumber: dailyItineraries.length + 1,
        title: `第${dailyItineraries.length + 1}天`,
        activities: dayActivities
      });
    }
    return dailyItineraries;
  }

  // 获取指定行程和日期的活动
  getDailyItinerary(tripId: number, dateString: string): DailyItinerary | null {
    const trip = this.getTripById(tripId);
    if (!trip) {
      return null;
    }
    const targetDate = new Date(dateString);
    const dailyItineraries = this.getTripDetails(tripId);
    return dailyItineraries.find(itinerary => itinerary.date.toDateString() === targetDate.toDateString()) || null;
  }

  // 添加活动
  addActivity(tripId: number, dateString: string, activityInput: ActivityInput): number {
    const trip = this.trips.find(t => t.id === tripId);
    if (!trip) {
      return -1; // 行程不存在
    }

    const newActivity: Activity = {
      id: this.nextActivityId++,
      tripId: tripId,
      date: new Date(dateString),
      title: activityInput.title,
      description: activityInput.description,
      startTime: activityInput.startTime,
      endTime: activityInput.endTime,
      location: activityInput.location,
      type: activityInput.type,
      completed: activityInput.completed
    };

    trip.activities.push(newActivity);
    trip.progress = this.calculateTripProgress(trip); // 更新行程进度
    return newActivity.id;
  }

  // 更新活动状态
  updateActivityStatus(tripId: number, activityId: number, completed: boolean): boolean {
    const trip = this.trips.find(t => t.id === tripId);
    if (!trip) return false;

    const activity = trip.activities.find(a => a.id === activityId);
    if (activity) {
      activity.completed = completed;
      trip.progress = this.calculateTripProgress(trip); // 更新行程进度
      return true;
    }
    return false;
  }

  // 获取活动 by ID
  getActivityById(tripId: number, activityId: number): ActivityQueryResult {
    const trip = this.getTripById(tripId);
    if (!trip) {
      const result: ActivityQueryResult = { trip: null, activity: null };
      return result;
    }
    const activity = trip.activities.find(a => a.id === activityId);
    const result: ActivityQueryResult = { trip: trip, activity: activity ? this.deepClone(activity) : null };
    return result;
  }

  // 更新活动
  updateActivity(tripId: number, activityId: number, updatedData: ActivityUpdateData): boolean {
    const trip = this.trips.find(t => t.id === tripId);
    if (!trip) return false;

    const activity = trip.activities.find(a => a.id === activityId);
    if (activity) {
      // 手动更新属性而不使用Object.assign
      if (updatedData.title !== undefined) activity.title = updatedData.title;
      if (updatedData.description !== undefined) activity.description = updatedData.description;
      if (updatedData.location !== undefined) activity.location = updatedData.location;
      if (updatedData.type !== undefined) activity.type = updatedData.type;
      if (updatedData.startTime !== undefined) activity.startTime = updatedData.startTime;
      if (updatedData.endTime !== undefined) activity.endTime = updatedData.endTime;
      if (updatedData.completed !== undefined) activity.completed = updatedData.completed;

      trip.progress = this.calculateTripProgress(trip); // 更新行程进度
      return true;
    }
    return false;
  }

  // 删除活动
  deleteActivity(tripId: number, activityId: number): boolean {
    const trip = this.trips.find(t => t.id === tripId);
    if (!trip) return false;

    const initialLength = trip.activities.length;
    trip.activities = trip.activities.filter(a => a.id !== activityId);
    trip.progress = this.calculateTripProgress(trip); // 更新行程进度
    return trip.activities.length < initialLength;
  }

  // 获取行程的所有日期字符串
  getTripDates(tripId: number): string[] {
    const trip = this.getTripById(tripId);
    if (!trip) return [];

    const dates: string[] = [];
    const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
    for (let d = new Date(trip.startDate); d <= trip.endDate; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0]);
    }
    return dates;
  }

  // 计算两个日期之间的天数
  private calculateDays(startDate: Date, endDate: Date): number {
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
  }

  // 计算行程进度
  private calculateTripProgress(trip: Trip): number {
    if (trip.activities.length === 0) {
      return 0;
    }
    const completedActivities = trip.activities.filter(activity => activity.completed).length;
    return Math.floor((completedActivities / trip.activities.length) * 100);
  }

  // 深拷贝对象
  private deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj), (key: string, value: string | number | boolean | object | null): string | number | boolean | object | null | Date => {
      if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(value)) {
        return new Date(value);
      }
      return value;
    }) as T;
  }
}

