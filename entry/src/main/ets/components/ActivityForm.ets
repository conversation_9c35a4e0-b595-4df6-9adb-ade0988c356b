/**
 * 活动表单组件
 * 用于添加和编辑活动的表单界面
 */

import { Activity, ActivityType } from '../models/TripModel';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

// 活动表单数据接口
export interface ActivityFormData {
  title: string;
  description?: string;
  location?: string;
  type: ActivityType;
  startTime: string;
  endTime: string;
}

// 活动类型选项接口
interface ActivityTypeOption {
  type: ActivityType;
  label: string;
  icon: string;
}

@Component
export struct ActivityForm {
  @Link formData: ActivityFormData;
  @State showLocationSuggestions: boolean = false;
  @State locationSuggestions: string[] = ['埃菲尔铁塔', '卢浮宫', '凯旋门', '香榭丽舍大街', '塞纳河游船'];
  
  // 活动类型选项
  private activityTypes: ActivityTypeOption[] = [
    { type: ActivityType.TRANSPORTATION, label: '交通', icon: '🚗' },
    { type: ActivityType.ACCOMMODATION, label: '住宿', icon: '🏨' },
    { type: ActivityType.DINING, label: '餐饮', icon: '🍽️' },
    { type: ActivityType.SHOPPING, label: '购物', icon: '🛍️' },
    { type: ActivityType.ENTERTAINMENT, label: '娱乐', icon: '🎭' },
    { type: ActivityType.SIGHTSEEING, label: '其他', icon: '📍' }
  ];

  // 获取活动类型标签
  getActivityTypeLabel(type: ActivityType): string {
    const typeInfo = this.activityTypes.find(t => t.type === type);
    return typeInfo ? typeInfo.label : '其他';
  }

  // 获取活动类型图标
  getActivityTypeIcon(type: ActivityType): string {
    const typeInfo = this.activityTypes.find(t => t.type === type);
    return typeInfo ? typeInfo.icon : '📍';
  }

  build() {
    Column() {
      // 活动标题
      Column() {
        Row() {
          Text('活动标题')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        TextInput({ placeholder: '输入活动标题', text: this.formData.title })
          .fontSize(16)
          .fontColor(THEME_COLORS.textPrimary)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 12, bottom: 12 })
          .onChange((value: string) => {
            this.formData.title = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 活动描述
      Column() {
        Text('活动描述')
          .fontSize(14)
          .fontColor(THEME_COLORS.textPrimary)
          .fontWeight(500)
          .width('100%')
          .margin({ bottom: 8 })

        TextArea({ placeholder: '输入活动描述', text: this.formData.description })
          .fontSize(16)
          .fontColor(THEME_COLORS.textPrimary)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 12, bottom: 12 })
          .height(80)
          .onChange((value: string) => {
            this.formData.description = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 活动地点
      Column() {
        Row() {
          Text('📍 活动地点')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
            .layoutWeight(1)

          Button() {
            Text('🔍')
              .fontSize(16)
              .fontColor(THEME_COLORS.primary)
          }
          .type(ButtonType.Circle)
          .backgroundColor('#E8F5E8')
          .width(32)
          .height(32)
          .onClick(() => {
            console.log('搜索地点');
          })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        // 地点输入框
        Stack() {
          TextInput({ 
            placeholder: '输入活动地点，如：埃菲尔铁塔、香榭丽舍', 
            text: this.formData.location 
          })
            .fontSize(16)
            .fontColor(THEME_COLORS.textPrimary)
            .backgroundColor('#FFF9E6')
            .borderRadius(8)
            .border({ width: 1, color: '#FFD700' })
            .padding({ left: 12, right: 40, top: 12, bottom: 12 })
            .onChange((value: string) => {
              this.formData.location = value;
              this.showLocationSuggestions = value.length > 0;
            })

          // 定位按钮
          Button() {
            Text('🎯')
              .fontSize(16)
              .fontColor(THEME_COLORS.primary)
          }
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .width(32)
          .height(32)
          .position({ x: '100%', y: '50%' })
          .translate({ x: -40, y: -16 })
          .onClick(() => {
            console.log('获取当前位置');
          })
        }
        .width('100%')

        // 地点建议列表
        if (this.showLocationSuggestions && this.formData.location.length > 0) {
          Column() {
            ForEach(this.locationSuggestions.filter(suggestion => 
              suggestion.includes(this.formData.location)
            ), (suggestion: string) => {
              Row() {
                Text('📍')
                  .fontSize(14)
                  .margin({ right: 8 })

                Text(suggestion)
                  .fontSize(14)
                  .fontColor(THEME_COLORS.textPrimary)
                  .layoutWeight(1)
              }
              .width('100%')
              .padding({ left: 12, right: 12, top: 8, bottom: 8 })
              .onClick(() => {
                this.formData.location = suggestion;
                this.showLocationSuggestions = false;
              })
            })
          }
          .width('100%')
          .backgroundColor(Color.White)
          .borderRadius(8)
          .border({ width: 1, color: '#E0E0E0' })
          .margin({ top: 4 })
          .height(120)
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 活动类型
      Column() {
        Row() {
          Text('活动类型')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 12 })

        // 活动类型网格
        Grid() {
          ForEach(this.activityTypes, (typeInfo: ActivityTypeOption) => {
            GridItem() {
              Column() {
                Text(typeInfo.icon)
                  .fontSize(24)
                  .margin({ bottom: 4 })

                Text(typeInfo.label)
                  .fontSize(12)
                  .fontColor(this.formData.type === typeInfo.type ? 
                    THEME_COLORS.primary : THEME_COLORS.textSecondary)
                  .fontWeight(this.formData.type === typeInfo.type ? 600 : 400)
              }
              .width('100%')
              .height(64)
              .justifyContent(FlexAlign.Center)
              .backgroundColor(this.formData.type === typeInfo.type ? 
                '#E8F5E8' : '#F5F5F5')
              .borderRadius(8)
              .border({ 
                width: this.formData.type === typeInfo.type ? 2 : 1, 
                color: this.formData.type === typeInfo.type ? 
                  THEME_COLORS.primary : '#E0E0E0' 
              })
              .onClick(() => {
                this.formData.type = typeInfo.type;
              })
            }
          })
        }
        .columnsTemplate('1fr 1fr 1fr')
        .rowsTemplate('1fr 1fr')
        .columnsGap(8)
        .rowsGap(8)
        .width('100%')
        .height(136)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 时间设置
      Row() {
        // 开始时间
        Column() {
          Text('开始时间')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
            .width('100%')
            .margin({ bottom: 8 })

          Row() {
            Text(this.formData.startTime || '选择时间')
              .fontSize(16)
              .fontColor(this.formData.startTime ? 
                THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
              .layoutWeight(1)

            Text('🕐')
              .fontSize(16)
          }
          .width('100%')
          .height(44)
          .padding({ left: 12, right: 12 })
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .alignItems(VerticalAlign.Center)
          .onClick(() => {
            console.log('选择开始时间');
            // TODO: 打开时间选择器
          })
        }
        .layoutWeight(1)

        Blank()
          .width(12)

        // 结束时间
        Column() {
          Text('时长(分钟)')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
            .width('100%')
            .margin({ bottom: 8 })

          Row() {
            Text(this.formData.endTime || '选择时间')
              .fontSize(16)
              .fontColor(this.formData.endTime ? 
                THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
              .layoutWeight(1)

            Text('⏱️')
              .fontSize(16)
          }
          .width('100%')
          .height(44)
          .padding({ left: 12, right: 12 })
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .alignItems(VerticalAlign.Center)
          .onClick(() => {
            console.log('选择结束时间');
            // TODO: 打开时间选择器
          })
        }
        .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 16 })
    }
    .width('100%')
    .padding({ left: 16, right: 16 })
  }
}
